// middleware.ts

import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";

const isProtectedRoute = createRouteMatcher([
  "/dashboard(.*)",
  "/learn(.*)",
  "/leaderboard(.*)",
  "/quests(.*)",
  "/shop(.*)",
  "/lesson(.*)",
  "/memorization(.*)",
  "/flashcard(.*)",
  "/nooraniQadia(.*)",
  "/tajweed(.*)",
  "/virtualClassroom(.*)",
  "/coursesPlayer(.*)",
  "/UstadTutor(.*)",
  "/api/user(.*)",
  "/api/courses(.*)",
  "/api/units(.*)",
  "/api/lessons(.*)",
  "/api/challenges(.*)",
  "/api/challenge-progress(.*)",
  "/api/user-progress(.*)",
  "/api/user-subscription(.*)",
  "/api/webhooks/stripe(.*)",
]);

export default clerkMiddleware((auth, req) => {
  if (isProtectedRoute(req)) {
    auth().protect();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};
